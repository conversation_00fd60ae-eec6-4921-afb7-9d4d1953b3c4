import requests
from msal import ConfidentialClientApplication

client_id = "29be3219-b709-4950-b119-53958725bc80"
client_secret = "****************************************"  
tenant_id = "1866d359-5399-426e-9d7b-0aa0fcfa0e8d"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scope = ["https://graph.microsoft.com/.default"]

app = ConfidentialClientApplication(
    client_id,
    authority=authority,
    client_credential=client_secret
)

token_response = app.acquire_token_for_client(scopes=scope)

if "access_token" in token_response:
    access_token = token_response["access_token"]
    print("Lấy token thành công.")

    site_hostname = "taureauai.sharepoint.com"
    site_path = "/sites/genai"
    folder_path = "Shared Documents/Test"

    site_resp = requests.get(
        f"https://graph.microsoft.com/v1.0/sites/{site_hostname}:{site_path}",
        headers={"Authorization": f"Bearer {access_token}"}
    )
    print("🔎 site_resp JSON:", site_resp.json())
    exit()

    drive_resp = requests.get(
        f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{folder_path}:/children",
        headers={"Authorization": f"Bearer {access_token}"}
    )

    print("📁 Danh sách file SharePoint:")
    for item in drive_resp.json().get("value", []):
        print("→", item["name"])

else:
    print("Lỗi lấy token:", token_response.get("error_description"))
