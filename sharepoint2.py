import requests
from msal import ConfidentialClientApplication

client_id = "29be3219-b709-4950-b119-53958725bc80"
client_secret = "****************************************"  
tenant_id = "9cb2bebd-6862-4d5f-8a77-ee591e704d99"
authority = f"https://login.microsoftonline.com/{tenant_id}"
scope = ["https://graph.microsoft.com/.default"]

app = ConfidentialClientApplication(
    client_id,
    authority=authority,
    client_credential=client_secret
)

token_response = app.acquire_token_for_client(scopes=scope)

if "access_token" in token_response:
    access_token = token_response["access_token"]
    print("Lấy token thành công.")

    site_hostname = "taureauai.sharepoint.com"
    site_path = "/sites/genai"
    folder_path = "Test"

    site_resp = requests.get(
        f"https://graph.microsoft.com/v1.0/sites/{site_hostname}:{site_path}",
        headers={"Authorization": f"Bearer {access_token}"}
    )

    if site_resp.status_code == 200:
        site_data = site_resp.json()
        site_id = site_data["id"]
        print("✅ Lấy thông tin site thành công.")
        print(f"📍 Site ID: {site_id}")
        print(f"📍 Site Name: {site_data['displayName']}")

        # First, let's check what's in the root directory
        print("🔍 Kiểm tra thư mục gốc...")
        root_resp = requests.get(
            f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root/children",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        if root_resp.status_code == 200:
            print("📁 Danh sách thư mục/file trong thư mục gốc:")
            root_items = root_resp.json().get("value", [])
            for item in root_items:
                file_type = "📁" if item.get("folder") else "📄"
                print(f"{file_type} {item['name']}")

        # Now try to get files from the specific folder
        print(f"\n🔍 Kiểm tra thư mục: {folder_path}")
        drive_resp = requests.get(
            f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{folder_path}:/children",
            headers={"Authorization": f"Bearer {access_token}"}
        )

        if drive_resp.status_code == 200:
            print(f"📁 Danh sách file trong thư mục '{folder_path}':")
            files = drive_resp.json().get("value", [])
            if files:
                for item in files:
                    file_type = "📁" if item.get("folder") else "📄"
                    print(f"{file_type} {item['name']}")
            else:
                print("📭 Thư mục trống hoặc không có file nào.")
        else:
            print(f"❌ Lỗi khi lấy danh sách file từ '{folder_path}': {drive_resp.status_code}")
            print("🔍 Chi tiết lỗi:", drive_resp.json())
            print("💡 Có thể thư mục không tồn tại hoặc đường dẫn không đúng.")
    else:
        print(f"❌ Lỗi khi lấy thông tin site: {site_resp.status_code}")
        print("🔍 Chi tiết lỗi:", site_resp.json())



else:
    print("Lỗi lấy token:", token_response.get("error_description"))
