import requests
import pandas as pd
from msal import ConfidentialClientApplication

# <PERSON><PERSON>u hình SharePoint
CONFIG = {
    "client_id": "29be3219-b709-4950-b119-53958725bc80",
    "client_secret": "****************************************",
    "tenant_id": "9cb2bebd-6862-4d5f-8a77-ee591e704d99",
    "site_hostname": "taureauai.sharepoint.com",
    "site_path": "/sites/genai",
    "folder_path": "Test"
}

def get_access_token():
    """Lấy access token từ Microsoft Graph API"""
    app = ConfidentialClientApplication(
        CONFIG["client_id"],
        authority=f"https://login.microsoftonline.com/{CONFIG['tenant_id']}",
        client_credential=CONFIG["client_secret"]
    )
    token_response = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
    return token_response.get("access_token")

def get_site_id(access_token):
    """Lấy Site ID từ SharePoint"""
    url = f"https://graph.microsoft.com/v1.0/sites/{CONFIG['site_hostname']}:{CONFIG['site_path']}"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})
    return response.json()["id"] if response.status_code == 200 else None

def download_and_analyze_excel(access_token, site_id, filename="cars_data_large.xlsx"):
    """Tải và phân tích file Excel từ SharePoint"""
    # Lấy danh sách file trong thư mục
    url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/root:/{CONFIG['folder_path']}:/children"
    response = requests.get(url, headers={"Authorization": f"Bearer {access_token}"})
    
    if response.status_code != 200:
        print(f"❌ Không thể truy cập thư mục: {response.status_code}")
        return
    
    files = response.json().get("value", [])
    excel_file = next((f for f in files if f['name'] == filename), None)
    
    if not excel_file:
        print(f"❌ Không tìm thấy file {filename}")
        return
    
    # Tải file Excel
    download_url = f"https://graph.microsoft.com/v1.0/sites/{site_id}/drive/items/{excel_file['id']}/content"
    download_response = requests.get(download_url, headers={"Authorization": f"Bearer {access_token}"})
    
    if download_response.status_code == 200:
        with open(filename, 'wb') as f:
            f.write(download_response.content)
        
        # Phân tích file Excel
        df = pd.read_excel(filename)
        print(f"✅ File {filename} đã được tải và phân tích!")
        print(f"📊 Kích thước: {len(df)} dòng × {len(df.columns)} cột")
        print(f"📋 Các cột: {list(df.columns)}")
        print(f"\n📋 Dữ liệu mẫu:")
        print(df.head())
    else:
        print(f"❌ Lỗi tải file: {download_response.status_code}")

# Chạy chương trình chính
if __name__ == "__main__":
    print("🔐 Đang xác thực...")
    access_token = get_access_token()
    
    if access_token:
        print("Xác thực thành công!")
        
        site_id = get_site_id(access_token)
        if site_id:
            print("✅ Kết nối SharePoint thành công!")
            download_and_analyze_excel(access_token, site_id)
        else:
            print("❌ Không thể kết nối SharePoint")
    else:
        print("❌ Xác thực thất bại")
